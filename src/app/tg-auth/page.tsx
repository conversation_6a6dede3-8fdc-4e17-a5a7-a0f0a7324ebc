"use client";

import Link from "next/link";
import TelegramAuth from "@/components/telegram-auth";
import TelegramAuthDev from "@/components/telegram-auth-dev";
import TelegramTest from "@/components/telegram-test";

export default function TelegramAuthPage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold mb-4">
            Telegram Authentication & Debugging
          </h1>
          <p className="text-lg text-muted-foreground mb-4">
            Test and debug Telegram Web App authentication integration
          </p>
          <Link
            href="/"
            className="text-blue-600 hover:text-blue-800 underline"
          >
            ← Back to Home
          </Link>
        </div>

        {/* Main Telegram Authentication */}
        <div className="mb-12 p-6 border rounded-lg bg-gradient-to-r from-blue-50 to-cyan-50">
          <h2 className="text-2xl font-semibold mb-4 text-center">
            Sign in with Telegram
          </h2>
          <p className="text-muted-foreground mb-6 text-center">
            Access the marketplace as a regular user through Telegram Web App
          </p>
          <TelegramAuth
            onSuccess={() => {
              window.location.href = "/profile";
            }}
            onError={(error) => {
              alert(`Authentication failed: ${error}`);
            }}
          />

          {/* Development Testing Component */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <TelegramAuthDev
              onSuccess={() => {
                window.location.href = "/profile";
              }}
              onError={(error) => {
                alert(`Development authentication failed: ${error}`);
              }}
            />
          </div>
        </div>

        {/* Debugging Information */}
        <div className="mb-8">
          <TelegramTest />
        </div>

        {/* Instructions */}
        <div className="p-6 border rounded-lg bg-blue-50">
          <h2 className="text-xl font-semibold mb-4">How to Test</h2>
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li>
              <strong>From Telegram Desktop/Mobile:</strong> Open this page through a Telegram bot or mini app
            </li>
            <li>
              <strong>Expected Results:</strong>
              <ul className="list-disc list-inside ml-4 mt-2 space-y-1">
                <li>&quot;Is in Telegram Web App&quot; should show &quot;Yes&quot;</li>
                <li>&quot;Is Initialized&quot; should show &quot;Yes&quot;</li>
                <li>Telegram Data should display user information</li>
                <li>window.Telegram.WebApp should show initData and initDataUnsafe</li>
              </ul>
            </li>
            <li>
              <strong>If not working:</strong>
              <ul className="list-disc list-inside ml-4 mt-2 space-y-1">
                <li>Check browser console for error messages</li>
                <li>Ensure you&apos;re opening from within Telegram</li>
                <li>Try the retry button if there are initialization errors</li>
                <li>Use the development mock authentication for testing</li>
              </ul>
            </li>
          </ol>
        </div>
      </div>
    </div>
  );
}
