"use client";

import { useEffect, useState, useCallback } from "react";
import {
  isTelegramWebApp,
  initTelegramWebApp,
  getTelegramWebAppData,
  TelegramWebAppInitData,
} from "@/utils/telegram-auth";

interface UseTelegramWebAppReturn {
  isInTelegram: boolean;
  isInitialized: boolean;
  telegramData: TelegramWebAppInitData | null;
  isLoading: boolean;
  error: string | null;
  retry: () => void;
}

export function useTelegramWebApp(): UseTelegramWebAppReturn {
  const [isInTelegram, setIsInTelegram] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [telegramData, setTelegramData] =
    useState<TelegramWebAppInitData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const initializeTelegram = useCallback(() => {
    console.log("[useTelegramWebApp] Starting initialization...");
    setIsLoading(true);
    setError(null);

    try {
      console.log("[useTelegramWebApp] Checking Telegram environment...");

      const inTelegram = isTelegramWebApp();
      console.log("[useTelegramWebApp] isTelegramWebApp result:", inTelegram);
      setIsInTelegram(inTelegram);

      if (!inTelegram) {
        console.log("[useTelegramWebApp] Not in Telegram environment");
        setIsLoading(false);
        return;
      }

      console.log("[useTelegramWebApp] Initializing Telegram Web App...");
      const initResult = initTelegramWebApp();
      console.log("[useTelegramWebApp] Init result:", initResult);

      if (!initResult) {
        throw new Error("Failed to initialize Telegram Web App");
      }

      // Get Telegram data immediately - no need to wait
      console.log("[useTelegramWebApp] Getting Telegram data...");
      const data = getTelegramWebAppData();
      console.log("[useTelegramWebApp] Telegram data:", data);

      if (!data) {
        console.warn("[useTelegramWebApp] No Telegram data available");
        // Don't throw error here, as this might be expected in some cases
      }

      setTelegramData(data);
      setIsInitialized(true);
      setError(null);

      console.log("[useTelegramWebApp] Initialization completed successfully");
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Unknown error during Telegram initialization";
      console.error("[useTelegramWebApp] Initialization error:", errorMessage);
      setError(errorMessage);
      setIsInitialized(false);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const retry = useCallback(() => {
    setRetryCount((prev) => prev + 1);
  }, []);

  useEffect(() => {
    if (typeof window === "undefined") {
      return;
    }

    // Initialize immediately - no delays needed with @twa-dev/sdk
    initializeTelegram();
  }, [initializeTelegram, retryCount]);

  return {
    isInTelegram,
    isInitialized,
    telegramData,
    isLoading,
    error,
    retry,
  };
}
